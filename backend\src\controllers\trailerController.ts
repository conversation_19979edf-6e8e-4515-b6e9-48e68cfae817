import { Response } from 'express';
import Joi from 'joi';
import { prisma } from '../services/database';
import {
  AuthenticatedRequest,
  ApiResponse,
  PaginatedResponse,
  CreateTrailerRequest,
  TrailerQuery,
  AppError
} from '../types';

// Validation schemas
const createTrailerSchema = Joi.object({
  licensePlate: Joi.string().min(1).max(20).required().messages({
    'string.min': 'License plate is required',
    'string.max': 'License plate cannot exceed 20 characters',
    'any.required': 'License plate is required'
  }),
  nickname: Joi.string().min(1).max(100).required().messages({
    'string.min': 'Nickname is required',
    'string.max': 'Nickname cannot exceed 100 characters',
    'any.required': 'Nickname is required'
  }),
  driverName: Joi.string().min(1).max(100).required().messages({
    'string.min': 'Driver name is required',
    'string.max': 'Driver name cannot exceed 100 characters',
    'any.required': 'Driver name is required'
  }),
  isRefrigerated: Joi.boolean().required(),
  fuelLevelPercent: Joi.number().min(0).max(100).allow(null),
  damageDetails: Joi.string().max(1000).allow('', null),
  hookCount: Joi.number().integer().min(0).required(),
  europalletCount: Joi.number().integer().min(0).required(),
  loadBarCount: Joi.number().integer().min(0).required(),
  lastInspectionDate: Joi.date().iso().required(),
  tires: Joi.array().items(
    Joi.object({
      position: Joi.string().required(),
      name: Joi.string().required(),
      condition: Joi.string().valid('NEW', 'GOOD', 'WORN', 'DAMAGED', 'REPLACE').required(),
      pressure: Joi.number().min(0).allow(null),
      depth: Joi.number().min(0).allow(null)
    })
  ).min(1).required()
});

const updateTrailerSchema = createTrailerSchema.fork(
  ['licensePlate', 'nickname', 'driverName', 'isRefrigerated', 'hookCount', 'europalletCount', 'loadBarCount', 'lastInspectionDate', 'tires'],
  (schema) => schema.optional()
);

class TrailerController {
  async getAll(req: AuthenticatedRequest, res: Response<PaginatedResponse<any>>): Promise<void> {
    const { page = '1', limit = '10', search, isRefrigerated, driverName }: TrailerQuery = req.query;
    
    const pageNum = Math.max(1, parseInt(page));
    const limitNum = Math.min(100, Math.max(1, parseInt(limit)));
    const skip = (pageNum - 1) * limitNum;

    // Build where clause
    const where: any = {
      userId: req.user!.id
    };

    if (search) {
      where.OR = [
        { licensePlate: { contains: search, mode: 'insensitive' } },
        { nickname: { contains: search, mode: 'insensitive' } },
        { driverName: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (isRefrigerated !== undefined) {
      where.isRefrigerated = isRefrigerated === 'true';
    }

    if (driverName) {
      where.driverName = { contains: driverName, mode: 'insensitive' };
    }

    // Get trailers with related data
    const [trailers, total] = await Promise.all([
      prisma.trailer.findMany({
        where,
        include: {
          tires: {
            orderBy: { position: 'asc' }
          },
          photos: {
            orderBy: { createdAt: 'desc' }
          },
          tireInspections: {
            orderBy: { inspectionDate: 'desc' },
            take: 1
          },
          refrigerationRefuelings: {
            orderBy: { refuelingDate: 'desc' },
            take: 5
          }
        },
        orderBy: { updatedAt: 'desc' },
        skip,
        take: limitNum
      }),
      prisma.trailer.count({ where })
    ]);

    const totalPages = Math.ceil(total / limitNum);

    res.json({
      success: true,
      data: trailers,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages
      },
      message: 'Trailers retrieved successfully'
    });
  }

  async getById(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    const { id } = req.params;

    const trailer = await prisma.trailer.findFirst({
      where: {
        id,
        userId: req.user!.id
      },
      include: {
        tires: {
          orderBy: { position: 'asc' }
        },
        photos: {
          orderBy: { createdAt: 'desc' }
        },
        tireInspections: {
          orderBy: { inspectionDate: 'desc' }
        },
        refrigerationRefuelings: {
          orderBy: { refuelingDate: 'desc' }
        }
      }
    });

    if (!trailer) {
      throw new AppError('Trailer not found', 404);
    }

    res.json({
      success: true,
      data: trailer,
      message: 'Trailer retrieved successfully'
    });
  }

  async create(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    // Validate request body
    const { error, value } = createTrailerSchema.validate(req.body);
    if (error) {
      throw new AppError(error.details[0].message, 400);
    }

    const trailerData: CreateTrailerRequest = value;

    // Create trailer with tires in a transaction
    const trailer = await prisma.$transaction(async (tx) => {
      // Create trailer
      const newTrailer = await tx.trailer.create({
        data: {
          licensePlate: trailerData.licensePlate,
          nickname: trailerData.nickname,
          driverName: trailerData.driverName,
          isRefrigerated: trailerData.isRefrigerated,
          fuelLevelPercent: trailerData.fuelLevelPercent,
          damageDetails: trailerData.damageDetails,
          hookCount: trailerData.hookCount,
          europalletCount: trailerData.europalletCount,
          loadBarCount: trailerData.loadBarCount,
          lastInspectionDate: new Date(trailerData.lastInspectionDate),
          userId: req.user!.id
        }
      });

      // Create tires
      await tx.tire.createMany({
        data: trailerData.tires.map(tire => ({
          ...tire,
          trailerId: newTrailer.id
        }))
      });

      // Return trailer with tires
      return tx.trailer.findUnique({
        where: { id: newTrailer.id },
        include: {
          tires: {
            orderBy: { position: 'asc' }
          }
        }
      });
    });

    res.status(201).json({
      success: true,
      data: trailer,
      message: 'Trailer created successfully'
    });
  }

  async update(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    const { id } = req.params;

    // Validate request body
    const { error, value } = updateTrailerSchema.validate(req.body);
    if (error) {
      throw new AppError(error.details[0].message, 400);
    }

    // Check if trailer exists and belongs to user
    const existingTrailer = await prisma.trailer.findFirst({
      where: {
        id,
        userId: req.user!.id
      }
    });

    if (!existingTrailer) {
      throw new AppError('Trailer not found', 404);
    }

    const trailerData = value;

    // Update trailer with tires in a transaction
    const trailer = await prisma.$transaction(async (tx) => {
      // Update trailer
      const updatedTrailer = await tx.trailer.update({
        where: { id },
        data: {
          ...(trailerData.licensePlate && { licensePlate: trailerData.licensePlate }),
          ...(trailerData.nickname && { nickname: trailerData.nickname }),
          ...(trailerData.driverName && { driverName: trailerData.driverName }),
          ...(trailerData.isRefrigerated !== undefined && { isRefrigerated: trailerData.isRefrigerated }),
          ...(trailerData.fuelLevelPercent !== undefined && { fuelLevelPercent: trailerData.fuelLevelPercent }),
          ...(trailerData.damageDetails !== undefined && { damageDetails: trailerData.damageDetails }),
          ...(trailerData.hookCount !== undefined && { hookCount: trailerData.hookCount }),
          ...(trailerData.europalletCount !== undefined && { europalletCount: trailerData.europalletCount }),
          ...(trailerData.loadBarCount !== undefined && { loadBarCount: trailerData.loadBarCount }),
          ...(trailerData.lastInspectionDate && { lastInspectionDate: new Date(trailerData.lastInspectionDate) })
        }
      });

      // Update tires if provided
      if (trailerData.tires) {
        // Delete existing tires
        await tx.tire.deleteMany({
          where: { trailerId: id }
        });

        // Create new tires
        await tx.tire.createMany({
          data: trailerData.tires.map(tire => ({
            ...tire,
            trailerId: id
          }))
        });
      }

      // Return updated trailer with tires
      return tx.trailer.findUnique({
        where: { id },
        include: {
          tires: {
            orderBy: { position: 'asc' }
          }
        }
      });
    });

    res.json({
      success: true,
      data: trailer,
      message: 'Trailer updated successfully'
    });
  }

  async delete(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    const { id } = req.params;

    // Check if trailer exists and belongs to user
    const trailer = await prisma.trailer.findFirst({
      where: {
        id,
        userId: req.user!.id
      }
    });

    if (!trailer) {
      throw new AppError('Trailer not found', 404);
    }

    // Delete trailer (cascade will handle related records)
    await prisma.trailer.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Trailer deleted successfully'
    });
  }
}

export const trailerController = new TrailerController();
