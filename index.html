
<!DOCTYPE html>
<html lang="cs">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Firemní <PERSON> s.r.o.</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {
              DEFAULT: '#79B837', // Mapler Green (button green from mapler.cz)
              light: '#9BD067',   // Lighter shade of Mapler Green
              dark: '#5E9A2A',    // Darker shade of Mapler Green
            },
            secondary: {
              DEFAULT: '#4A4A4A', // Dark grey (from Mapler logo text)
            },
            neutral: {
              light: '#F9FAFB', // gray-50
              DEFAULT: '#F3F4F6', // gray-100
              dark: '#4B5563', // gray-600
            }
          }
        }
      }
    }
  </script>
<script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.3.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-neutral">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>