import { Router, Request, Response } from 'express';
import DatabaseService from '@/services/database';
import { ApiResponse } from '@/types';

const router = Router();

interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: 'healthy' | 'unhealthy';
  };
}

/**
 * @swagger
 * /health:
 *   get:
 *     summary: Health check endpoint
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     status:
 *                       type: string
 *                       enum: [healthy, unhealthy]
 *                     timestamp:
 *                       type: string
 *                       format: date-time
 *                     uptime:
 *                       type: number
 *                     version:
 *                       type: string
 *                     environment:
 *                       type: string
 *                     services:
 *                       type: object
 *                       properties:
 *                         database:
 *                           type: string
 *                           enum: [healthy, unhealthy]
 *       503:
 *         description: Service is unhealthy
 */
router.get('/', async (req: Request, res: Response<ApiResponse<HealthCheckResponse>>) => {
  try {
    // Check database health
    const isDatabaseHealthy = await DatabaseService.healthCheck();
    
    const healthData: HealthCheckResponse = {
      status: isDatabaseHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.API_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: isDatabaseHealthy ? 'healthy' : 'unhealthy'
      }
    };

    const statusCode = healthData.status === 'healthy' ? 200 : 503;

    res.status(statusCode).json({
      success: healthData.status === 'healthy',
      data: healthData,
      message: `Service is ${healthData.status}`
    });
  } catch (error) {
    res.status(503).json({
      success: false,
      data: {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.API_VERSION || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        services: {
          database: 'unhealthy'
        }
      } as HealthCheckResponse,
      message: 'Service is unhealthy',
      error: 'Health check failed'
    });
  }
});

export default router;
