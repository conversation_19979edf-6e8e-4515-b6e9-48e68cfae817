// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  trailers              Trailer[]
  tractorRefuelings     TractorRefueling[]
  createdInspections    TireInspection[]

  @@map("users")
}

model Trailer {
  id                    String    @id @default(cuid())
  licensePlate          String    @unique
  nickname              String
  driverName            String
  isRefrigerated        Boolean   @default(false)
  fuelLevelPercent      Float?
  damageDetails         String?
  hookCount             Int       @default(0)
  europalletCount       Int       @default(0)
  loadBarCount          Int       @default(0)
  lastInspectionDate    DateTime
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  // Relations
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  tires                 Tire[]
  photos                TrailerPhoto[]
  tireInspections       TireInspection[]
  refrigerationRefuelings RefrigerationRefueling[]

  @@map("trailers")
}

model Tire {
  id          String        @id @default(cuid())
  position    String        // e.g., "tire1_axle1_left"
  name        String        // e.g., "Náprava 1 Levá"
  condition   TireCondition @default(GOOD)
  pressure    Float?        // in Bar
  depth       Float?        // in mm
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  trailerId   String
  trailer     Trailer       @relation(fields: [trailerId], references: [id], onDelete: Cascade)

  @@unique([trailerId, position])
  @@map("tires")
}

model TrailerPhoto {
  id            String      @id @default(cuid())
  type          PhotoType
  filename      String
  originalName  String
  mimeType      String
  size          Int
  url           String
  capturedAt    DateTime
  latitude      Float?
  longitude     Float?
  accuracy      Float?
  createdAt     DateTime    @default(now())

  // Relations
  trailerId     String
  trailer       Trailer     @relation(fields: [trailerId], references: [id], onDelete: Cascade)

  @@map("trailer_photos")
}

model TireInspection {
  id                    String    @id @default(cuid())
  inspectionDate        DateTime
  odometerKm            Int?
  purpose               String
  mechanicName          String
  recommendedPressure   Float?    // in Bar
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  // Relations
  trailerId             String
  trailer               Trailer   @relation(fields: [trailerId], references: [id], onDelete: Cascade)
  
  userId                String
  user                  User      @relation(fields: [userId], references: [id])

  @@map("tire_inspections")
}

model TractorRefueling {
  id                String    @id @default(cuid())
  tractorLicensePlate String
  refuelingDate     DateTime
  dieselLiters      Float?
  adblueLiters      Float?
  odometerKm        Int?
  receiptPhotoUrl   String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  userId            String
  user              User      @relation(fields: [userId], references: [id])

  @@map("tractor_refuelings")
}

model RefrigerationRefueling {
  id                String    @id @default(cuid())
  refuelingDate     DateTime
  dieselLiters      Float?
  fridgeMth         Float?    // Moto-hours
  receiptPhotoUrl   String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  trailerId         String
  trailer           Trailer   @relation(fields: [trailerId], references: [id], onDelete: Cascade)

  @@map("refrigeration_refuelings")
}

enum UserRole {
  ADMIN
  USER
}

enum TireCondition {
  NEW       // Nová
  GOOD      // Dobrý stav
  WORN      // Opotřebená
  DAMAGED   // Poškozená
  REPLACE   // Nutná výměna
}

enum PhotoType {
  RIGHT_SIDE      // photoRightSide
  REAR           // photoRear
  LEFT_SIDE      // photoLeftSide
  TIRE_DAMAGE_1  // tireDamagePhoto1
  TIRE_DAMAGE_2  // tireDamagePhoto2
  RECEIPT        // receiptPhoto
}
