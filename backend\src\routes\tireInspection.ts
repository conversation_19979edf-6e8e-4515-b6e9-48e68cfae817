import { Router } from 'express';
import { tireInspectionController } from '@/controllers/tireInspectionController';
import { asyncHand<PERSON> } from '@/middleware/errorHandler';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     TireInspection:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         inspectionDate:
 *           type: string
 *           format: date-time
 *         odometerKm:
 *           type: integer
 *           nullable: true
 *         purpose:
 *           type: string
 *         mechanicName:
 *           type: string
 *         recommendedPressure:
 *           type: number
 *           nullable: true
 *         trailerId:
 *           type: string
 *         trailer:
 *           $ref: '#/components/schemas/Trailer'
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *     CreateTireInspectionRequest:
 *       type: object
 *       required:
 *         - trailerId
 *         - inspectionDate
 *         - purpose
 *         - mechanicName
 *       properties:
 *         trailerId:
 *           type: string
 *         inspectionDate:
 *           type: string
 *           format: date-time
 *         odometerKm:
 *           type: integer
 *         purpose:
 *           type: string
 *         mechanicName:
 *           type: string
 *         recommendedPressure:
 *           type: number
 */

/**
 * @swagger
 * /tire-inspections:
 *   get:
 *     summary: Get all tire inspection records
 *     tags: [Tire Inspections]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: trailerId
 *         schema:
 *           type: string
 *         description: Filter by trailer ID
 *       - in: query
 *         name: mechanicName
 *         schema:
 *           type: string
 *         description: Filter by mechanic name
 *     responses:
 *       200:
 *         description: Tire inspection records retrieved successfully
 *   post:
 *     summary: Create a new tire inspection record
 *     tags: [Tire Inspections]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateTireInspectionRequest'
 *     responses:
 *       201:
 *         description: Tire inspection record created successfully
 */
router.get('/', asyncHandler(tireInspectionController.getAll));
router.post('/', asyncHandler(tireInspectionController.create));

/**
 * @swagger
 * /tire-inspections/{id}:
 *   get:
 *     summary: Get tire inspection record by ID
 *     tags: [Tire Inspections]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Tire inspection record retrieved successfully
 *       404:
 *         description: Record not found
 *   put:
 *     summary: Update tire inspection record
 *     tags: [Tire Inspections]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateTireInspectionRequest'
 *     responses:
 *       200:
 *         description: Tire inspection record updated successfully
 *       404:
 *         description: Record not found
 *   delete:
 *     summary: Delete tire inspection record
 *     tags: [Tire Inspections]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Tire inspection record deleted successfully
 *       404:
 *         description: Record not found
 */
router.get('/:id', asyncHandler(tireInspectionController.getById));
router.put('/:id', asyncHandler(tireInspectionController.update));
router.delete('/:id', asyncHandler(tireInspectionController.delete));

export default router;
