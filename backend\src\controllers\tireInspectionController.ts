import { Response } from 'express';
import Jo<PERSON> from 'joi';
import { prisma } from '@/services/database';
import { 
  AuthenticatedRequest, 
  ApiResponse, 
  PaginatedResponse,
  CreateTireInspectionRequest,
  AppError 
} from '@/types';

// Validation schemas
const createTireInspectionSchema = Joi.object({
  trailerId: Joi.string().required().messages({
    'any.required': 'Trailer ID is required'
  }),
  inspectionDate: Joi.date().iso().required().messages({
    'any.required': 'Inspection date is required'
  }),
  odometerKm: Joi.number().integer().min(0).allow(null),
  purpose: Joi.string().min(1).max(500).required().messages({
    'string.min': 'Purpose is required',
    'string.max': 'Purpose cannot exceed 500 characters',
    'any.required': 'Purpose is required'
  }),
  mechanicName: Joi.string().min(1).max(100).required().messages({
    'string.min': 'Mechanic name is required',
    'string.max': 'Mechanic name cannot exceed 100 characters',
    'any.required': 'Mechanic name is required'
  }),
  recommendedPressure: Joi.number().min(0).allow(null)
});

const updateTireInspectionSchema = createTireInspectionSchema.fork(
  ['trailerId', 'inspectionDate', 'purpose', 'mechanicName'],
  (schema) => schema.optional()
);

class TireInspectionController {
  async getAll(req: AuthenticatedRequest, res: Response<PaginatedResponse<any>>): Promise<void> {
    const { page = '1', limit = '10', trailerId, mechanicName } = req.query;
    
    const pageNum = Math.max(1, parseInt(page as string));
    const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
    const skip = (pageNum - 1) * limitNum;

    // Build where clause
    const where: any = {
      trailer: {
        userId: req.user!.id
      }
    };

    if (trailerId) {
      where.trailerId = trailerId as string;
    }

    if (mechanicName) {
      where.mechanicName = { contains: mechanicName as string, mode: 'insensitive' };
    }

    // Get inspection records with trailer info
    const [inspections, total] = await Promise.all([
      prisma.tireInspection.findMany({
        where,
        include: {
          trailer: {
            select: {
              id: true,
              licensePlate: true,
              nickname: true,
              driverName: true
            }
          }
        },
        orderBy: { inspectionDate: 'desc' },
        skip,
        take: limitNum
      }),
      prisma.tireInspection.count({ where })
    ]);

    const totalPages = Math.ceil(total / limitNum);

    res.json({
      success: true,
      data: inspections,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages
      },
      message: 'Tire inspection records retrieved successfully'
    });
  }

  async getById(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    const { id } = req.params;

    const inspection = await prisma.tireInspection.findFirst({
      where: {
        id,
        trailer: {
          userId: req.user!.id
        }
      },
      include: {
        trailer: {
          select: {
            id: true,
            licensePlate: true,
            nickname: true,
            driverName: true
          }
        }
      }
    });

    if (!inspection) {
      throw new AppError('Tire inspection record not found', 404);
    }

    res.json({
      success: true,
      data: inspection,
      message: 'Tire inspection record retrieved successfully'
    });
  }

  async create(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    // Validate request body
    const { error, value } = createTireInspectionSchema.validate(req.body);
    if (error) {
      throw new AppError(error.details[0].message, 400);
    }

    const inspectionData: CreateTireInspectionRequest = value;

    // Check if trailer exists and belongs to user
    const trailer = await prisma.trailer.findFirst({
      where: {
        id: inspectionData.trailerId,
        userId: req.user!.id
      }
    });

    if (!trailer) {
      throw new AppError('Trailer not found', 404);
    }

    // Create inspection record
    const inspection = await prisma.tireInspection.create({
      data: {
        inspectionDate: new Date(inspectionData.inspectionDate),
        odometerKm: inspectionData.odometerKm,
        purpose: inspectionData.purpose,
        mechanicName: inspectionData.mechanicName,
        recommendedPressure: inspectionData.recommendedPressure,
        trailerId: inspectionData.trailerId,
        userId: req.user!.id
      },
      include: {
        trailer: {
          select: {
            id: true,
            licensePlate: true,
            nickname: true,
            driverName: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: inspection,
      message: 'Tire inspection record created successfully'
    });
  }

  async update(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    const { id } = req.params;

    // Validate request body
    const { error, value } = updateTireInspectionSchema.validate(req.body);
    if (error) {
      throw new AppError(error.details[0].message, 400);
    }

    // Check if record exists and belongs to user
    const existingInspection = await prisma.tireInspection.findFirst({
      where: {
        id,
        trailer: {
          userId: req.user!.id
        }
      }
    });

    if (!existingInspection) {
      throw new AppError('Tire inspection record not found', 404);
    }

    const inspectionData = value;

    // If trailerId is being updated, check if new trailer belongs to user
    if (inspectionData.trailerId) {
      const trailer = await prisma.trailer.findFirst({
        where: {
          id: inspectionData.trailerId,
          userId: req.user!.id
        }
      });

      if (!trailer) {
        throw new AppError('Trailer not found', 404);
      }
    }

    // Update inspection record
    const inspection = await prisma.tireInspection.update({
      where: { id },
      data: {
        ...(inspectionData.inspectionDate && { inspectionDate: new Date(inspectionData.inspectionDate) }),
        ...(inspectionData.odometerKm !== undefined && { odometerKm: inspectionData.odometerKm }),
        ...(inspectionData.purpose && { purpose: inspectionData.purpose }),
        ...(inspectionData.mechanicName && { mechanicName: inspectionData.mechanicName }),
        ...(inspectionData.recommendedPressure !== undefined && { recommendedPressure: inspectionData.recommendedPressure }),
        ...(inspectionData.trailerId && { trailerId: inspectionData.trailerId })
      },
      include: {
        trailer: {
          select: {
            id: true,
            licensePlate: true,
            nickname: true,
            driverName: true
          }
        }
      }
    });

    res.json({
      success: true,
      data: inspection,
      message: 'Tire inspection record updated successfully'
    });
  }

  async delete(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    const { id } = req.params;

    // Check if record exists and belongs to user
    const inspection = await prisma.tireInspection.findFirst({
      where: {
        id,
        trailer: {
          userId: req.user!.id
        }
      }
    });

    if (!inspection) {
      throw new AppError('Tire inspection record not found', 404);
    }

    // Delete inspection record
    await prisma.tireInspection.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Tire inspection record deleted successfully'
    });
  }
}

export const tireInspectionController = new TireInspectionController();
