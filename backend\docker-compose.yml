version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: mapler-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: mapler_portal
      POSTGRES_USER: mapler_user
      POSTGRES_PASSWORD: mapler_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - mapler-network

  redis:
    image: redis:7-alpine
    container_name: mapler-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - mapler-network

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mapler-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3001
      DATABASE_URL: ******************************************************/mapler_portal
      REDIS_URL: redis://redis:6379
      JWT_SECRET: mapler-super-secret-jwt-key-2024-production
      JWT_EXPIRES_IN: 7d
      UPLOAD_MAX_SIZE: 10485760
      CORS_ORIGIN: http://localhost:3000
    ports:
      - "3001:3001"
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - mapler-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  mapler-network:
    driver: bridge
