version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: mapler-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: mapler_portal
      POSTGRES_USER: mapler_user
      POSTGRES_PASSWORD: mapler_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - mapler-network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: mapler-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - mapler-network

  # Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mapler-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3001
      DATABASE_URL: ******************************************************/mapler_portal
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      JWT_EXPIRES_IN: 7d
      UPLOAD_MAX_SIZE: 10485760  # 10MB
      CORS_ORIGIN: http://localhost:3000
    ports:
      - "3001:3001"
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - mapler-network

  # pgAdmin for database management (optional, for development)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: mapler-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - mapler-network
    profiles:
      - dev

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  mapler-network:
    driver: bridge
