import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ing<PERSON><PERSON>ord, 
  RefrigerationUnitRefuelingEntry, 
  TireInspectionData,
  PhotoMetadata 
} from '../types';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

// API Response types
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string[]>;
}

interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface AuthResponse {
  user: {
    id: string;
    email: string;
    name: string;
    role: string;
    createdAt: string;
    updatedAt: string;
  };
  token: string;
}

// Auth types
interface LoginRequest {
  email: string;
  password: string;
}

interface RegisterRequest {
  email: string;
  password: string;
  name: string;
}

class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('auth_token');
  }

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  private async uploadRequest<T>(
    endpoint: string,
    formData: FormData
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {};
    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || 'Upload failed');
      }

      return data;
    } catch (error) {
      console.error('Upload request failed:', error);
      throw error;
    }
  }

  setToken(token: string | null) {
    this.token = token;
    if (token) {
      localStorage.setItem('auth_token', token);
    } else {
      localStorage.removeItem('auth_token');
    }
  }

  // Auth methods
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
    
    if (response.data?.token) {
      this.setToken(response.data.token);
    }
    
    return response.data!;
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
    
    if (response.data?.token) {
      this.setToken(response.data.token);
    }
    
    return response.data!;
  }

  async getProfile() {
    const response = await this.request('/auth/me');
    return response.data;
  }

  logout() {
    this.setToken(null);
  }

  // Trailer methods
  async getTrailers(params?: {
    page?: number;
    limit?: number;
    search?: string;
    isRefrigerated?: boolean;
    driverName?: string;
  }): Promise<PaginatedResponse<Trailer>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);
    if (params?.isRefrigerated !== undefined) searchParams.append('isRefrigerated', params.isRefrigerated.toString());
    if (params?.driverName) searchParams.append('driverName', params.driverName);

    const query = searchParams.toString();
    return this.request<Trailer[]>(`/trailers${query ? `?${query}` : ''}`);
  }

  async getTrailer(id: string): Promise<Trailer> {
    const response = await this.request<Trailer>(`/trailers/${id}`);
    return response.data!;
  }

  async createTrailer(trailer: Omit<Trailer, 'id' | 'createdAt' | 'updatedAt'>): Promise<Trailer> {
    const response = await this.request<Trailer>('/trailers', {
      method: 'POST',
      body: JSON.stringify(trailer),
    });
    return response.data!;
  }

  async updateTrailer(id: string, trailer: Partial<Trailer>): Promise<Trailer> {
    const response = await this.request<Trailer>(`/trailers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(trailer),
    });
    return response.data!;
  }

  async deleteTrailer(id: string): Promise<void> {
    await this.request(`/trailers/${id}`, {
      method: 'DELETE',
    });
  }

  // Upload methods
  async uploadTrailerPhoto(
    file: File,
    metadata: {
      type: string;
      trailerId: string;
      capturedAt: string;
      latitude?: number;
      longitude?: number;
      accuracy?: number;
    }
  ): Promise<PhotoMetadata> {
    const formData = new FormData();
    formData.append('photo', file);
    formData.append('type', metadata.type);
    formData.append('trailerId', metadata.trailerId);
    formData.append('capturedAt', metadata.capturedAt);
    if (metadata.latitude) formData.append('latitude', metadata.latitude.toString());
    if (metadata.longitude) formData.append('longitude', metadata.longitude.toString());
    if (metadata.accuracy) formData.append('accuracy', metadata.accuracy.toString());

    const response = await this.uploadRequest<PhotoMetadata>('/upload/trailer-photo', formData);
    return response.data!;
  }

  async uploadReceiptPhoto(
    file: File,
    metadata: {
      capturedAt: string;
      latitude?: number;
      longitude?: number;
      accuracy?: number;
    }
  ): Promise<{ url: string; filename: string }> {
    const formData = new FormData();
    formData.append('photo', file);
    formData.append('capturedAt', metadata.capturedAt);
    if (metadata.latitude) formData.append('latitude', metadata.latitude.toString());
    if (metadata.longitude) formData.append('longitude', metadata.longitude.toString());
    if (metadata.accuracy) formData.append('accuracy', metadata.accuracy.toString());

    const response = await this.uploadRequest<{ url: string; filename: string }>('/upload/receipt-photo', formData);
    return response.data!;
  }

  async deletePhoto(id: string): Promise<void> {
    await this.request(`/upload/photos/${id}`, {
      method: 'DELETE',
    });
  }

  // Tractor refueling methods
  async getTractorRefuelings(params?: {
    page?: number;
    limit?: number;
    tractorLicensePlate?: string;
  }): Promise<PaginatedResponse<TractorRefuelingRecord>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.tractorLicensePlate) searchParams.append('tractorLicensePlate', params.tractorLicensePlate);

    const query = searchParams.toString();
    return this.request<TractorRefuelingRecord[]>(`/tractor-refueling${query ? `?${query}` : ''}`);
  }

  async createTractorRefueling(refueling: Omit<TractorRefuelingRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<TractorRefuelingRecord> {
    const response = await this.request<TractorRefuelingRecord>('/tractor-refueling', {
      method: 'POST',
      body: JSON.stringify(refueling),
    });
    return response.data!;
  }

  // Refrigeration refueling methods
  async getRefrigerationRefuelings(params?: {
    page?: number;
    limit?: number;
    trailerId?: string;
  }): Promise<PaginatedResponse<RefrigerationUnitRefuelingEntry>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.trailerId) searchParams.append('trailerId', params.trailerId);

    const query = searchParams.toString();
    return this.request<RefrigerationUnitRefuelingEntry[]>(`/refrigeration-refueling${query ? `?${query}` : ''}`);
  }

  async createRefrigerationRefueling(refueling: Omit<RefrigerationUnitRefuelingEntry, 'id' | 'createdAt' | 'updatedAt'>): Promise<RefrigerationUnitRefuelingEntry> {
    const response = await this.request<RefrigerationUnitRefuelingEntry>('/refrigeration-refueling', {
      method: 'POST',
      body: JSON.stringify(refueling),
    });
    return response.data!;
  }

  // Tire inspection methods
  async getTireInspections(params?: {
    page?: number;
    limit?: number;
    trailerId?: string;
    mechanicName?: string;
  }): Promise<PaginatedResponse<TireInspectionData>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.trailerId) searchParams.append('trailerId', params.trailerId);
    if (params?.mechanicName) searchParams.append('mechanicName', params.mechanicName);

    const query = searchParams.toString();
    return this.request<TireInspectionData[]>(`/tire-inspections${query ? `?${query}` : ''}`);
  }

  async createTireInspection(inspection: Omit<TireInspectionData, 'id' | 'createdAt' | 'updatedAt'>): Promise<TireInspectionData> {
    const response = await this.request<TireInspectionData>('/tire-inspections', {
      method: 'POST',
      body: JSON.stringify(inspection),
    });
    return response.data!;
  }
}

// Create and export API client instance
export const apiClient = new ApiClient(API_BASE_URL);
export default apiClient;
