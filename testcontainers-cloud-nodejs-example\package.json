{"name": "testcontainers-cloud-nodejs-example", "version": "1.0.0", "description": "The current repository helps you to verify that you configured your [Testcontainers Cloud][tcc] agent correctly in your local environment.", "main": "index.js", "engines": {"npm": ">=8.0.0", "node": ">=16.0.0"}, "scripts": {"test": "jest --testTimeout=180000"}, "repository": {"type": "git", "url": "git+https://github.com/AtomicJar/testcontainers-cloud-nodejs-example.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/AtomicJar/testcontainers-cloud-nodejs-example/issues"}, "homepage": "https://github.com/AtomicJar/testcontainers-cloud-nodejs-example#readme", "devDependencies": {"@testcontainers/postgresql": "^10.3.2", "jest": "^29.7.0", "run-script-os": "^1.1.6", "testcontainers": "^10.18.0", "@types/pg": "^8.10.9", "pg": "^8.11.3"}, "dependencies": {"async-redis": "^2.0.0"}}