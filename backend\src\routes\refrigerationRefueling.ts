import { Router } from 'express';
import { refrigerationRefuelingController } from '../controllers/refrigerationRefuelingController';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     RefrigerationRefueling:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         refuelingDate:
 *           type: string
 *           format: date-time
 *         dieselLiters:
 *           type: number
 *           nullable: true
 *         fridgeMth:
 *           type: number
 *           nullable: true
 *         receiptPhotoUrl:
 *           type: string
 *           nullable: true
 *         trailerId:
 *           type: string
 *         trailer:
 *           $ref: '#/components/schemas/Trailer'
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *     CreateRefrigerationRefuelingRequest:
 *       type: object
 *       required:
 *         - trailerId
 *         - refuelingDate
 *       properties:
 *         trailerId:
 *           type: string
 *         refuelingDate:
 *           type: string
 *           format: date-time
 *         dieselLiters:
 *           type: number
 *         fridgeMth:
 *           type: number
 *         receiptPhotoUrl:
 *           type: string
 */

/**
 * @swagger
 * /refrigeration-refueling:
 *   get:
 *     summary: Get all refrigeration refueling records
 *     tags: [Refrigeration Refueling]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: trailerId
 *         schema:
 *           type: string
 *         description: Filter by trailer ID
 *     responses:
 *       200:
 *         description: Refrigeration refueling records retrieved successfully
 *   post:
 *     summary: Create a new refrigeration refueling record
 *     tags: [Refrigeration Refueling]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateRefrigerationRefuelingRequest'
 *     responses:
 *       201:
 *         description: Refrigeration refueling record created successfully
 */
router.get('/', asyncHandler(refrigerationRefuelingController.getAll));
router.post('/', asyncHandler(refrigerationRefuelingController.create));

/**
 * @swagger
 * /refrigeration-refueling/{id}:
 *   get:
 *     summary: Get refrigeration refueling record by ID
 *     tags: [Refrigeration Refueling]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Refrigeration refueling record retrieved successfully
 *       404:
 *         description: Record not found
 *   put:
 *     summary: Update refrigeration refueling record
 *     tags: [Refrigeration Refueling]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateRefrigerationRefuelingRequest'
 *     responses:
 *       200:
 *         description: Refrigeration refueling record updated successfully
 *       404:
 *         description: Record not found
 *   delete:
 *     summary: Delete refrigeration refueling record
 *     tags: [Refrigeration Refueling]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Refrigeration refueling record deleted successfully
 *       404:
 *         description: Record not found
 */
router.get('/:id', asyncHandler(refrigerationRefuelingController.getById));
router.put('/:id', asyncHandler(refrigerationRefuelingController.update));
router.delete('/:id', asyncHandler(refrigerationRefuelingController.delete));

export default router;
