# Mapler Portal Backend

Backend API pro portál společnosti Mapler s.r.o. - systém pro správu návěsů a evidenci technického stavu.

## 🚀 Technologie

- **Node.js** + **Express** + **TypeScript**
- **PostgreSQL** databáze s **Prisma ORM**
- **Docker** + **Docker Compose** pro kontejnerizaci
- **JWT** autentifikace
- **Swagger/OpenAPI** dokumentace
- **Multer** pro upload souborů
- **Joi** validace
- **bcryptjs** pro hashování hesel

## 📋 Funkcionality

### Autentifikace
- Registrace a přihlášení uživatelů
- JWT tokeny s expirací
- Role-based access control (ADMIN, USER)

### Správa návěsů
- CRUD operace pro návěsy
- Správa pneumatik s detailními informacemi
- Upload fotografií s GPS metadaty
- Filtrace a vyhledávání

### Evidence tankování
- <PERSON><PERSON><PERSON><PERSON> (nafta, AdBlue)
- Tankování chlad<PERSON>c<PERSON>ch jed<PERSON>k
- Upload účtenek

### Kontroly pneumatik
- Záznamy o kontrolách
- Doporučený tlak
- Historie kontrol

## 🛠️ Instalace a spuštění

### Předpoklady
- Node.js 18+
- Docker a Docker Compose
- PostgreSQL (nebo použijte Docker)

### 1. Klonování a instalace
```bash
cd backend
npm install
```

### 2. Konfigurace prostředí
```bash
cp .env.example .env
# Upravte .env soubor podle potřeby
```

### 3. Spuštění s Dockerem (doporučeno)
```bash
# Spustí PostgreSQL, Redis a backend
docker-compose up -d

# Pro development s live reload
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```

### 4. Nebo lokální spuštění
```bash
# Spustit pouze databázi
docker-compose up postgres redis -d

# Migrace databáze
npm run db:push

# Seed data
npm run db:seed

# Spustit development server
npm run dev
```

## 📚 API Dokumentace

Po spuštění je dostupná na:
- **Swagger UI**: http://localhost:3001/api-docs
- **Health Check**: http://localhost:3001/health

### Hlavní endpointy

#### Autentifikace
- `POST /api/auth/register` - Registrace
- `POST /api/auth/login` - Přihlášení
- `GET /api/auth/me` - Profil uživatele

#### Návěsy
- `GET /api/trailers` - Seznam návěsů
- `POST /api/trailers` - Vytvoření návěsu
- `GET /api/trailers/:id` - Detail návěsu
- `PUT /api/trailers/:id` - Aktualizace návěsu
- `DELETE /api/trailers/:id` - Smazání návěsu

#### Upload souborů
- `POST /api/upload/trailer-photo` - Upload fotografie návěsu
- `POST /api/upload/receipt-photo` - Upload účtenky
- `DELETE /api/upload/photos/:id` - Smazání fotografie

#### Tankování tahačů
- `GET /api/tractor-refueling` - Seznam tankování
- `POST /api/tractor-refueling` - Nové tankování
- `PUT /api/tractor-refueling/:id` - Aktualizace
- `DELETE /api/tractor-refueling/:id` - Smazání

#### Tankování chladících jednotek
- `GET /api/refrigeration-refueling` - Seznam tankování
- `POST /api/refrigeration-refueling` - Nové tankování
- `PUT /api/refrigeration-refueling/:id` - Aktualizace
- `DELETE /api/refrigeration-refueling/:id` - Smazání

#### Kontroly pneumatik
- `GET /api/tire-inspections` - Seznam kontrol
- `POST /api/tire-inspections` - Nová kontrola
- `PUT /api/tire-inspections/:id` - Aktualizace
- `DELETE /api/tire-inspections/:id` - Smazání

## 🗄️ Databázové schéma

### Hlavní tabulky
- `users` - Uživatelé systému
- `trailers` - Návěsy
- `tires` - Pneumatiky návěsů
- `trailer_photos` - Fotografie návěsů
- `tire_inspections` - Kontroly pneumatik
- `tractor_refuelings` - Tankování tahačů
- `refrigeration_refuelings` - Tankování chladících jednotek

## 🔧 Development

### Užitečné příkazy
```bash
# Development server s live reload
npm run dev

# Build pro produkci
npm run build

# Spuštění produkční verze
npm start

# Prisma příkazy
npm run db:generate    # Generování Prisma klienta
npm run db:push        # Push schématu do DB
npm run db:migrate     # Vytvoření migrace
npm run db:studio      # Prisma Studio GUI
npm run db:seed        # Seed testovacích dat

# Testování
npm test
npm run test:watch

# Linting
npm run lint
npm run lint:fix
```

### Testovací účty
Po spuštění seed skriptu:
- **Admin**: <EMAIL> / admin123
- **User**: <EMAIL> / user123

## 🐳 Docker

### Development
```bash
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```

### Production
```bash
docker-compose up -d
```

### Pouze databáze
```bash
docker-compose up postgres redis -d
```

## 🔒 Bezpečnost

- JWT tokeny s expirací
- Bcrypt hashování hesel (12 rounds)
- Rate limiting (100 req/15min)
- CORS konfigurace
- Helmet.js security headers
- Input validace s Joi
- File upload omezení

## 📁 Struktura projektu

```
backend/
├── src/
│   ├── controllers/     # Business logika
│   ├── middleware/      # Express middleware
│   ├── routes/         # API routes
│   ├── services/       # Databázové služby
│   ├── types/          # TypeScript typy
│   └── app.ts          # Hlavní Express app
├── prisma/
│   ├── schema.prisma   # Databázové schéma
│   └── seed.ts         # Seed data
├── uploads/            # Nahrané soubory
├── Dockerfile
├── docker-compose.yml
└── package.json
```

## 🚀 Deployment

1. Nastavte produkční environment variables
2. Spusťte `docker-compose up -d`
3. Spusťte migrace: `docker-compose exec backend npm run db:migrate`
4. Volitelně seed data: `docker-compose exec backend npm run db:seed`

## 📞 Podpora

Pro technickou podporu kontaktujte vývojový tým Mapler s.r.o.
