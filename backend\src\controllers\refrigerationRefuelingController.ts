import { Response } from 'express';
import Jo<PERSON> from 'joi';
import { prisma } from '@/services/database';
import { 
  AuthenticatedRequest, 
  ApiResponse, 
  PaginatedResponse,
  CreateRefrigerationRefuelingRequest,
  AppError 
} from '@/types';

// Validation schemas
const createRefrigerationRefuelingSchema = Joi.object({
  trailerId: Joi.string().required().messages({
    'any.required': 'Trailer ID is required'
  }),
  refuelingDate: Joi.date().iso().required().messages({
    'any.required': 'Refueling date is required'
  }),
  dieselLiters: Joi.number().min(0).allow(null),
  fridgeMth: Joi.number().min(0).allow(null),
  receiptPhotoUrl: Joi.string().uri().allow('', null)
});

const updateRefrigerationRefuelingSchema = createRefrigerationRefuelingSchema.fork(
  ['trailerId', 'refuelingDate'],
  (schema) => schema.optional()
);

class RefrigerationRefuelingController {
  async getAll(req: AuthenticatedRequest, res: Response<PaginatedResponse<any>>): Promise<void> {
    const { page = '1', limit = '10', trailerId } = req.query;
    
    const pageNum = Math.max(1, parseInt(page as string));
    const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
    const skip = (pageNum - 1) * limitNum;

    // Build where clause
    const where: any = {
      trailer: {
        userId: req.user!.id
      }
    };

    if (trailerId) {
      where.trailerId = trailerId as string;
    }

    // Get refueling records with trailer info
    const [refuelings, total] = await Promise.all([
      prisma.refrigerationRefueling.findMany({
        where,
        include: {
          trailer: {
            select: {
              id: true,
              licensePlate: true,
              nickname: true,
              driverName: true
            }
          }
        },
        orderBy: { refuelingDate: 'desc' },
        skip,
        take: limitNum
      }),
      prisma.refrigerationRefueling.count({ where })
    ]);

    const totalPages = Math.ceil(total / limitNum);

    res.json({
      success: true,
      data: refuelings,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages
      },
      message: 'Refrigeration refueling records retrieved successfully'
    });
  }

  async getById(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    const { id } = req.params;

    const refueling = await prisma.refrigerationRefueling.findFirst({
      where: {
        id,
        trailer: {
          userId: req.user!.id
        }
      },
      include: {
        trailer: {
          select: {
            id: true,
            licensePlate: true,
            nickname: true,
            driverName: true
          }
        }
      }
    });

    if (!refueling) {
      throw new AppError('Refrigeration refueling record not found', 404);
    }

    res.json({
      success: true,
      data: refueling,
      message: 'Refrigeration refueling record retrieved successfully'
    });
  }

  async create(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    // Validate request body
    const { error, value } = createRefrigerationRefuelingSchema.validate(req.body);
    if (error) {
      throw new AppError(error.details[0].message, 400);
    }

    const refuelingData: CreateRefrigerationRefuelingRequest = value;

    // Check if trailer exists and belongs to user
    const trailer = await prisma.trailer.findFirst({
      where: {
        id: refuelingData.trailerId,
        userId: req.user!.id
      }
    });

    if (!trailer) {
      throw new AppError('Trailer not found', 404);
    }

    // Create refueling record
    const refueling = await prisma.refrigerationRefueling.create({
      data: {
        refuelingDate: new Date(refuelingData.refuelingDate),
        dieselLiters: refuelingData.dieselLiters,
        fridgeMth: refuelingData.fridgeMth,
        receiptPhotoUrl: refuelingData.receiptPhotoUrl,
        trailerId: refuelingData.trailerId
      },
      include: {
        trailer: {
          select: {
            id: true,
            licensePlate: true,
            nickname: true,
            driverName: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: refueling,
      message: 'Refrigeration refueling record created successfully'
    });
  }

  async update(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    const { id } = req.params;

    // Validate request body
    const { error, value } = updateRefrigerationRefuelingSchema.validate(req.body);
    if (error) {
      throw new AppError(error.details[0].message, 400);
    }

    // Check if record exists and belongs to user
    const existingRefueling = await prisma.refrigerationRefueling.findFirst({
      where: {
        id,
        trailer: {
          userId: req.user!.id
        }
      }
    });

    if (!existingRefueling) {
      throw new AppError('Refrigeration refueling record not found', 404);
    }

    const refuelingData = value;

    // If trailerId is being updated, check if new trailer belongs to user
    if (refuelingData.trailerId) {
      const trailer = await prisma.trailer.findFirst({
        where: {
          id: refuelingData.trailerId,
          userId: req.user!.id
        }
      });

      if (!trailer) {
        throw new AppError('Trailer not found', 404);
      }
    }

    // Update refueling record
    const refueling = await prisma.refrigerationRefueling.update({
      where: { id },
      data: {
        ...(refuelingData.refuelingDate && { refuelingDate: new Date(refuelingData.refuelingDate) }),
        ...(refuelingData.dieselLiters !== undefined && { dieselLiters: refuelingData.dieselLiters }),
        ...(refuelingData.fridgeMth !== undefined && { fridgeMth: refuelingData.fridgeMth }),
        ...(refuelingData.receiptPhotoUrl !== undefined && { receiptPhotoUrl: refuelingData.receiptPhotoUrl }),
        ...(refuelingData.trailerId && { trailerId: refuelingData.trailerId })
      },
      include: {
        trailer: {
          select: {
            id: true,
            licensePlate: true,
            nickname: true,
            driverName: true
          }
        }
      }
    });

    res.json({
      success: true,
      data: refueling,
      message: 'Refrigeration refueling record updated successfully'
    });
  }

  async delete(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    const { id } = req.params;

    // Check if record exists and belongs to user
    const refueling = await prisma.refrigerationRefueling.findFirst({
      where: {
        id,
        trailer: {
          userId: req.user!.id
        }
      }
    });

    if (!refueling) {
      throw new AppError('Refrigeration refueling record not found', 404);
    }

    // Delete refueling record
    await prisma.refrigerationRefueling.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Refrigeration refueling record deleted successfully'
    });
  }
}

export const refrigerationRefuelingController = new RefrigerationRefuelingController();
