import { Response } from 'express';
import fs from 'fs/promises';
import path from 'path';
import Joi from 'joi';
import { prisma } from '@/services/database';
import { 
  AuthenticatedRequest, 
  ApiResponse, 
  UploadPhotoRequest,
  AppError 
} from '@/types';

// Validation schemas
const trailerPhotoSchema = Joi.object({
  type: Joi.string().valid('RIGHT_SIDE', 'REAR', 'LEFT_SIDE', 'TIRE_DAMAGE_1', 'TIRE_DAMAGE_2').required(),
  trailerId: Joi.string().required(),
  capturedAt: Joi.date().iso().required(),
  latitude: Joi.number().min(-90).max(90).allow(null),
  longitude: Joi.number().min(-180).max(180).allow(null),
  accuracy: Joi.number().min(0).allow(null)
});

const receiptPhotoSchema = Joi.object({
  capturedAt: Joi.date().iso().required(),
  latitude: Joi.number().min(-90).max(90).allow(null),
  longitude: Joi.number().min(-180).max(180).allow(null),
  accuracy: Joi.number().min(0).allow(null)
});

class UploadController {
  async uploadTrailerPhoto(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    if (!req.file) {
      throw new AppError('No file uploaded', 400);
    }

    // Validate request body
    const { error, value } = trailerPhotoSchema.validate(req.body);
    if (error) {
      // Clean up uploaded file on validation error
      await this.cleanupFile(req.file.path);
      throw new AppError(error.details[0].message, 400);
    }

    const { type, trailerId, capturedAt, latitude, longitude, accuracy }: UploadPhotoRequest = value;

    // Check if trailer exists and belongs to user
    const trailer = await prisma.trailer.findFirst({
      where: {
        id: trailerId!,
        userId: req.user!.id
      }
    });

    if (!trailer) {
      // Clean up uploaded file if trailer not found
      await this.cleanupFile(req.file.path);
      throw new AppError('Trailer not found', 404);
    }

    try {
      // Create photo record in database
      const photo = await prisma.trailerPhoto.create({
        data: {
          type: type as any,
          filename: req.file.filename,
          originalName: req.file.originalname,
          mimeType: req.file.mimetype,
          size: req.file.size,
          url: `/uploads/${req.file.filename}`,
          capturedAt: new Date(capturedAt),
          latitude,
          longitude,
          accuracy,
          trailerId: trailerId!
        }
      });

      res.status(201).json({
        success: true,
        data: photo,
        message: 'Photo uploaded successfully'
      });
    } catch (error) {
      // Clean up uploaded file on database error
      await this.cleanupFile(req.file.path);
      throw error;
    }
  }

  async uploadReceiptPhoto(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    if (!req.file) {
      throw new AppError('No file uploaded', 400);
    }

    // Validate request body
    const { error, value } = receiptPhotoSchema.validate(req.body);
    if (error) {
      // Clean up uploaded file on validation error
      await this.cleanupFile(req.file.path);
      throw new AppError(error.details[0].message, 400);
    }

    // For receipt photos, we just return the file URL without storing in database
    // The URL will be stored in the refueling record
    res.status(201).json({
      success: true,
      data: {
        url: `/uploads/${req.file.filename}`,
        filename: req.file.filename,
        originalName: req.file.originalname,
        mimeType: req.file.mimetype,
        size: req.file.size
      },
      message: 'Receipt photo uploaded successfully'
    });
  }

  async deletePhoto(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    const { id } = req.params;

    // Find photo and check if it belongs to user's trailer
    const photo = await prisma.trailerPhoto.findFirst({
      where: {
        id,
        trailer: {
          userId: req.user!.id
        }
      }
    });

    if (!photo) {
      throw new AppError('Photo not found', 404);
    }

    try {
      // Delete photo from database
      await prisma.trailerPhoto.delete({
        where: { id }
      });

      // Delete file from filesystem
      const filePath = path.join(process.cwd(), 'uploads', photo.filename);
      await this.cleanupFile(filePath);

      res.json({
        success: true,
        message: 'Photo deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting photo:', error);
      throw new AppError('Failed to delete photo', 500);
    }
  }

  private async cleanupFile(filePath: string): Promise<void> {
    try {
      await fs.unlink(filePath);
    } catch (error) {
      console.error('Error cleaning up file:', filePath, error);
      // Don't throw error for cleanup failures
    }
  }
}

export const uploadController = new UploadController();
