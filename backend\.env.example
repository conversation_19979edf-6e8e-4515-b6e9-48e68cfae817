# Database Configuration
DATABASE_URL="******************************************************/mapler_portal"

# Redis Configuration
REDIS_URL="redis://redis:6379"

# JWT Configuration
JWT_SECRET="mapler-super-secret-jwt-key-2024-production"
JWT_EXPIRES_IN="7d"

# Server Configuration
NODE_ENV="development"
PORT=3001

# CORS Configuration
CORS_ORIGIN="http://localhost:3000"

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760  # 10MB in bytes
UPLOAD_ALLOWED_TYPES="image/jpeg,image/png,image/webp,application/pdf"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL="info"

# Company Information
COMPANY_NAME="Mapler s.r.o."
COMPANY_EMAIL="<EMAIL>"

# API Documentation
API_TITLE="Mapler Portal API"
API_DESCRIPTION="REST API for Mapler s.r.o. company portal - trailer management system"
API_VERSION="1.0.0"
