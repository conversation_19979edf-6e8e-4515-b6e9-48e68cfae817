import { Router } from 'express';
import { tractorRefuelingController } from '@/controllers/tractorRefuelingController';
import { asyncHandler } from '@/middleware/errorHandler';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     TractorRefueling:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         tractorLicensePlate:
 *           type: string
 *         refuelingDate:
 *           type: string
 *           format: date-time
 *         dieselLiters:
 *           type: number
 *           nullable: true
 *         adblueLiters:
 *           type: number
 *           nullable: true
 *         odometerKm:
 *           type: integer
 *           nullable: true
 *         receiptPhotoUrl:
 *           type: string
 *           nullable: true
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *     CreateTractorRefuelingRequest:
 *       type: object
 *       required:
 *         - tractorLicensePlate
 *         - refuelingDate
 *       properties:
 *         tractorLicensePlate:
 *           type: string
 *         refuelingDate:
 *           type: string
 *           format: date-time
 *         dieselLiters:
 *           type: number
 *         adblueLiters:
 *           type: number
 *         odometerKm:
 *           type: integer
 *         receiptPhotoUrl:
 *           type: string
 */

/**
 * @swagger
 * /tractor-refueling:
 *   get:
 *     summary: Get all tractor refueling records
 *     tags: [Tractor Refueling]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: tractorLicensePlate
 *         schema:
 *           type: string
 *         description: Filter by tractor license plate
 *     responses:
 *       200:
 *         description: Tractor refueling records retrieved successfully
 *   post:
 *     summary: Create a new tractor refueling record
 *     tags: [Tractor Refueling]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateTractorRefuelingRequest'
 *     responses:
 *       201:
 *         description: Tractor refueling record created successfully
 */
router.get('/', asyncHandler(tractorRefuelingController.getAll));
router.post('/', asyncHandler(tractorRefuelingController.create));

/**
 * @swagger
 * /tractor-refueling/{id}:
 *   get:
 *     summary: Get tractor refueling record by ID
 *     tags: [Tractor Refueling]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Tractor refueling record retrieved successfully
 *       404:
 *         description: Record not found
 *   put:
 *     summary: Update tractor refueling record
 *     tags: [Tractor Refueling]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateTractorRefuelingRequest'
 *     responses:
 *       200:
 *         description: Tractor refueling record updated successfully
 *       404:
 *         description: Record not found
 *   delete:
 *     summary: Delete tractor refueling record
 *     tags: [Tractor Refueling]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Tractor refueling record deleted successfully
 *       404:
 *         description: Record not found
 */
router.get('/:id', asyncHandler(tractorRefuelingController.getById));
router.put('/:id', asyncHandler(tractorRefuelingController.update));
router.delete('/:id', asyncHandler(tractorRefuelingController.delete));

export default router;
