import { Router } from 'express';
import { trailer<PERSON>ontroller } from '@/controllers/trailerController';
import { async<PERSON>and<PERSON> } from '@/middleware/errorHandler';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Trailer:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         licensePlate:
 *           type: string
 *         nickname:
 *           type: string
 *         driverName:
 *           type: string
 *         isRefrigerated:
 *           type: boolean
 *         fuelLevelPercent:
 *           type: number
 *           nullable: true
 *         damageDetails:
 *           type: string
 *           nullable: true
 *         hookCount:
 *           type: integer
 *         europalletCount:
 *           type: integer
 *         loadBarCount:
 *           type: integer
 *         lastInspectionDate:
 *           type: string
 *           format: date-time
 *         tires:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Tire'
 *         photos:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/TrailerPhoto'
 *     Tire:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         position:
 *           type: string
 *         name:
 *           type: string
 *         condition:
 *           type: string
 *           enum: [NE<PERSON>, GO<PERSON>, W<PERSON><PERSON>, DAMAGED, REPLACE]
 *         pressure:
 *           type: number
 *           nullable: true
 *         depth:
 *           type: number
 *           nullable: true
 *     CreateTrailerRequest:
 *       type: object
 *       required:
 *         - licensePlate
 *         - nickname
 *         - driverName
 *         - isRefrigerated
 *         - hookCount
 *         - europalletCount
 *         - loadBarCount
 *         - lastInspectionDate
 *         - tires
 *       properties:
 *         licensePlate:
 *           type: string
 *         nickname:
 *           type: string
 *         driverName:
 *           type: string
 *         isRefrigerated:
 *           type: boolean
 *         fuelLevelPercent:
 *           type: number
 *         damageDetails:
 *           type: string
 *         hookCount:
 *           type: integer
 *         europalletCount:
 *           type: integer
 *         loadBarCount:
 *           type: integer
 *         lastInspectionDate:
 *           type: string
 *           format: date-time
 *         tires:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               position:
 *                 type: string
 *               name:
 *                 type: string
 *               condition:
 *                 type: string
 *                 enum: [NEW, GOOD, WORN, DAMAGED, REPLACE]
 *               pressure:
 *                 type: number
 *               depth:
 *                 type: number
 */

/**
 * @swagger
 * /trailers:
 *   get:
 *     summary: Get all trailers
 *     tags: [Trailers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by license plate, nickname, or driver name
 *       - in: query
 *         name: isRefrigerated
 *         schema:
 *           type: boolean
 *         description: Filter by refrigerated status
 *     responses:
 *       200:
 *         description: Trailers retrieved successfully
 *   post:
 *     summary: Create a new trailer
 *     tags: [Trailers]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateTrailerRequest'
 *     responses:
 *       201:
 *         description: Trailer created successfully
 */
router.get('/', asyncHandler(trailerController.getAll));
router.post('/', asyncHandler(trailerController.create));

/**
 * @swagger
 * /trailers/{id}:
 *   get:
 *     summary: Get trailer by ID
 *     tags: [Trailers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Trailer retrieved successfully
 *       404:
 *         description: Trailer not found
 *   put:
 *     summary: Update trailer
 *     tags: [Trailers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateTrailerRequest'
 *     responses:
 *       200:
 *         description: Trailer updated successfully
 *       404:
 *         description: Trailer not found
 *   delete:
 *     summary: Delete trailer
 *     tags: [Trailers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Trailer deleted successfully
 *       404:
 *         description: Trailer not found
 */
router.get('/:id', asyncHandler(trailerController.getById));
router.put('/:id', asyncHandler(trailerController.update));
router.delete('/:id', asyncHandler(trailerController.delete));

export default router;
