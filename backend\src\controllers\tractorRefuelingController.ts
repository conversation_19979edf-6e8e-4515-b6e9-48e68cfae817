import { Response } from 'express';
import Joi from 'joi';
import { prisma } from '@/services/database';
import { 
  AuthenticatedRequest, 
  ApiResponse, 
  PaginatedResponse,
  CreateTractorRefuelingRequest,
  AppError 
} from '@/types';

// Validation schemas
const createTractorRefuelingSchema = Joi.object({
  tractorLicensePlate: Joi.string().min(1).max(20).required().messages({
    'string.min': 'Tractor license plate is required',
    'string.max': 'Tractor license plate cannot exceed 20 characters',
    'any.required': 'Tractor license plate is required'
  }),
  refuelingDate: Joi.date().iso().required().messages({
    'any.required': 'Refueling date is required'
  }),
  dieselLiters: Joi.number().min(0).allow(null),
  adblueLiters: Joi.number().min(0).allow(null),
  odometerKm: Joi.number().integer().min(0).allow(null),
  receiptPhotoUrl: Joi.string().uri().allow('', null)
});

const updateTractorRefuelingSchema = createTractorRefuelingSchema.fork(
  ['tractorLicensePlate', 'refuelingDate'],
  (schema) => schema.optional()
);

class TractorRefuelingController {
  async getAll(req: AuthenticatedRequest, res: Response<PaginatedResponse<any>>): Promise<void> {
    const { page = '1', limit = '10', tractorLicensePlate } = req.query;
    
    const pageNum = Math.max(1, parseInt(page as string));
    const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
    const skip = (pageNum - 1) * limitNum;

    // Build where clause
    const where: any = {
      userId: req.user!.id
    };

    if (tractorLicensePlate) {
      where.tractorLicensePlate = { contains: tractorLicensePlate as string, mode: 'insensitive' };
    }

    // Get refueling records
    const [refuelings, total] = await Promise.all([
      prisma.tractorRefueling.findMany({
        where,
        orderBy: { refuelingDate: 'desc' },
        skip,
        take: limitNum
      }),
      prisma.tractorRefueling.count({ where })
    ]);

    const totalPages = Math.ceil(total / limitNum);

    res.json({
      success: true,
      data: refuelings,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages
      },
      message: 'Tractor refueling records retrieved successfully'
    });
  }

  async getById(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    const { id } = req.params;

    const refueling = await prisma.tractorRefueling.findFirst({
      where: {
        id,
        userId: req.user!.id
      }
    });

    if (!refueling) {
      throw new AppError('Tractor refueling record not found', 404);
    }

    res.json({
      success: true,
      data: refueling,
      message: 'Tractor refueling record retrieved successfully'
    });
  }

  async create(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    // Validate request body
    const { error, value } = createTractorRefuelingSchema.validate(req.body);
    if (error) {
      throw new AppError(error.details[0].message, 400);
    }

    const refuelingData: CreateTractorRefuelingRequest = value;

    // Create refueling record
    const refueling = await prisma.tractorRefueling.create({
      data: {
        tractorLicensePlate: refuelingData.tractorLicensePlate,
        refuelingDate: new Date(refuelingData.refuelingDate),
        dieselLiters: refuelingData.dieselLiters,
        adblueLiters: refuelingData.adblueLiters,
        odometerKm: refuelingData.odometerKm,
        receiptPhotoUrl: refuelingData.receiptPhotoUrl,
        userId: req.user!.id
      }
    });

    res.status(201).json({
      success: true,
      data: refueling,
      message: 'Tractor refueling record created successfully'
    });
  }

  async update(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    const { id } = req.params;

    // Validate request body
    const { error, value } = updateTractorRefuelingSchema.validate(req.body);
    if (error) {
      throw new AppError(error.details[0].message, 400);
    }

    // Check if record exists and belongs to user
    const existingRefueling = await prisma.tractorRefueling.findFirst({
      where: {
        id,
        userId: req.user!.id
      }
    });

    if (!existingRefueling) {
      throw new AppError('Tractor refueling record not found', 404);
    }

    const refuelingData = value;

    // Update refueling record
    const refueling = await prisma.tractorRefueling.update({
      where: { id },
      data: {
        ...(refuelingData.tractorLicensePlate && { tractorLicensePlate: refuelingData.tractorLicensePlate }),
        ...(refuelingData.refuelingDate && { refuelingDate: new Date(refuelingData.refuelingDate) }),
        ...(refuelingData.dieselLiters !== undefined && { dieselLiters: refuelingData.dieselLiters }),
        ...(refuelingData.adblueLiters !== undefined && { adblueLiters: refuelingData.adblueLiters }),
        ...(refuelingData.odometerKm !== undefined && { odometerKm: refuelingData.odometerKm }),
        ...(refuelingData.receiptPhotoUrl !== undefined && { receiptPhotoUrl: refuelingData.receiptPhotoUrl })
      }
    });

    res.json({
      success: true,
      data: refueling,
      message: 'Tractor refueling record updated successfully'
    });
  }

  async delete(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    const { id } = req.params;

    // Check if record exists and belongs to user
    const refueling = await prisma.tractorRefueling.findFirst({
      where: {
        id,
        userId: req.user!.id
      }
    });

    if (!refueling) {
      throw new AppError('Tractor refueling record not found', 404);
    }

    // Delete refueling record
    await prisma.tractorRefueling.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Tractor refueling record deleted successfully'
    });
  }
}

export const tractorRefuelingController = new TractorRefuelingController();
