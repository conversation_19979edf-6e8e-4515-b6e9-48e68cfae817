{"name": "mapler-portal-backend", "version": "1.0.0", "description": "Backend API for Mapler s.r.o. company portal - trailer management system", "main": "dist/app.js", "scripts": {"dev": "nodemon src/app.ts", "build": "tsc", "start": "node dist/app.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["nodejs", "express", "typescript", "prisma", "postgresql", "docker", "api", "trailer-management"], "author": "Mapler s.r.o.", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "joi": "^17.11.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.10.5", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/compression": "^1.7.5", "@types/swagger-ui-express": "^4.1.6", "@types/swagger-jsdoc": "^6.0.4", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^5.3.3", "prisma": "^5.7.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}