import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 12);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: adminPassword,
      name: 'Admin User',
      role: 'ADMIN'
    }
  });

  console.log('✅ Admin user created:', admin.email);

  // Create test user
  const userPassword = await bcrypt.hash('user123', 12);
  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: userPassword,
      name: 'Test User',
      role: 'USER'
    }
  });

  console.log('✅ Test user created:', user.email);

  // Create sample trailers
  const trailer1 = await prisma.trailer.upsert({
    where: { licensePlate: '1T12345' },
    update: {},
    create: {
      licensePlate: '1T12345',
      nickname: 'Návěs Alpha',
      driverName: 'Jan Novák',
      isRefrigerated: true,
      fuelLevelPercent: 75.5,
      damageDetails: 'Malé škrábance na pravé straně',
      hookCount: 8,
      europalletCount: 33,
      loadBarCount: 12,
      lastInspectionDate: new Date('2024-01-15'),
      userId: user.id
    }
  });

  const trailer2 = await prisma.trailer.upsert({
    where: { licensePlate: '1T67890' },
    update: {},
    create: {
      licensePlate: '1T67890',
      nickname: 'Návěs Beta',
      driverName: 'Petr Svoboda',
      isRefrigerated: false,
      fuelLevelPercent: 50.0,
      hookCount: 6,
      europalletCount: 30,
      loadBarCount: 10,
      lastInspectionDate: new Date('2024-01-10'),
      userId: user.id
    }
  });

  console.log('✅ Sample trailers created');

  // Create sample tires for trailer1
  const tires1 = [
    { position: 'tire1_axle1_left', name: 'Náprava 1 Levá', condition: 'GOOD' },
    { position: 'tire2_axle1_right', name: 'Náprava 1 Pravá', condition: 'GOOD' },
    { position: 'tire3_axle2_left', name: 'Náprava 2 Levá', condition: 'WORN' },
    { position: 'tire4_axle2_right', name: 'Náprava 2 Pravá', condition: 'GOOD' },
    { position: 'tire5_axle3_left', name: 'Náprava 3 Levá', condition: 'NEW' },
    { position: 'tire6_axle3_right', name: 'Náprava 3 Pravá', condition: 'NEW' }
  ];

  for (const tire of tires1) {
    await prisma.tire.upsert({
      where: { 
        trailerId_position: { 
          trailerId: trailer1.id, 
          position: tire.position 
        } 
      },
      update: {},
      create: {
        ...tire,
        condition: tire.condition as any,
        pressure: 8.5,
        depth: 7.2,
        trailerId: trailer1.id
      }
    });
  }

  // Create sample tires for trailer2
  const tires2 = [
    { position: 'tire1_axle1_left', name: 'Náprava 1 Levá', condition: 'GOOD' },
    { position: 'tire2_axle1_right', name: 'Náprava 1 Pravá', condition: 'DAMAGED' },
    { position: 'tire3_axle2_left', name: 'Náprava 2 Levá', condition: 'GOOD' },
    { position: 'tire4_axle2_right', name: 'Náprava 2 Pravá', condition: 'WORN' },
    { position: 'tire5_axle3_left', name: 'Náprava 3 Levá', condition: 'GOOD' },
    { position: 'tire6_axle3_right', name: 'Náprava 3 Pravá', condition: 'REPLACE' }
  ];

  for (const tire of tires2) {
    await prisma.tire.upsert({
      where: { 
        trailerId_position: { 
          trailerId: trailer2.id, 
          position: tire.position 
        } 
      },
      update: {},
      create: {
        ...tire,
        condition: tire.condition as any,
        pressure: 8.0,
        depth: tire.condition === 'REPLACE' ? 1.5 : 6.8,
        trailerId: trailer2.id
      }
    });
  }

  console.log('✅ Sample tires created');

  // Create sample tire inspection
  await prisma.tireInspection.create({
    data: {
      inspectionDate: new Date('2024-01-15'),
      odometerKm: 125000,
      purpose: 'Pravidelná kontrola před dlouhou trasou',
      mechanicName: 'Tomáš Mechanic',
      recommendedPressure: 8.5,
      trailerId: trailer1.id,
      userId: user.id
    }
  });

  console.log('✅ Sample tire inspection created');

  // Create sample tractor refueling
  await prisma.tractorRefueling.create({
    data: {
      tractorLicensePlate: '1A12345',
      refuelingDate: new Date('2024-01-20'),
      dieselLiters: 450.5,
      adblueLiters: 25.0,
      odometerKm: 180000,
      userId: user.id
    }
  });

  console.log('✅ Sample tractor refueling created');

  // Create sample refrigeration refueling
  await prisma.refrigerationRefueling.create({
    data: {
      refuelingDate: new Date('2024-01-18'),
      dieselLiters: 85.2,
      fridgeMth: 1250.5,
      trailerId: trailer1.id
    }
  });

  console.log('✅ Sample refrigeration refueling created');

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📋 Test accounts:');
  console.log('Admin: <EMAIL> / admin123');
  console.log('User: <EMAIL> / user123');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
