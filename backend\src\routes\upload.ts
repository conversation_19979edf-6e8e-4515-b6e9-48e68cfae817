import { Router } from 'express';
import { upload<PERSON>ontroller } from '@/controllers/uploadController';
import { uploadSingle } from '@/middleware/upload';
import { asyncHandler } from '@/middleware/errorHandler';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     TrailerPhoto:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         type:
 *           type: string
 *           enum: [RIGHT_SIDE, REAR, LEFT_SIDE, TIRE_DAMAGE_1, TIRE_DAMAGE_2, RECEIPT]
 *         filename:
 *           type: string
 *         originalName:
 *           type: string
 *         mimeType:
 *           type: string
 *         size:
 *           type: integer
 *         url:
 *           type: string
 *         capturedAt:
 *           type: string
 *           format: date-time
 *         latitude:
 *           type: number
 *           nullable: true
 *         longitude:
 *           type: number
 *           nullable: true
 *         accuracy:
 *           type: number
 *           nullable: true
 *         createdAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /upload/trailer-photo:
 *   post:
 *     summary: Upload a trailer photo
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - photo
 *               - type
 *               - trailerId
 *               - capturedAt
 *             properties:
 *               photo:
 *                 type: string
 *                 format: binary
 *               type:
 *                 type: string
 *                 enum: [RIGHT_SIDE, REAR, LEFT_SIDE, TIRE_DAMAGE_1, TIRE_DAMAGE_2]
 *               trailerId:
 *                 type: string
 *               capturedAt:
 *                 type: string
 *                 format: date-time
 *               latitude:
 *                 type: number
 *               longitude:
 *                 type: number
 *               accuracy:
 *                 type: number
 *     responses:
 *       201:
 *         description: Photo uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/TrailerPhoto'
 *                 message:
 *                   type: string
 *       400:
 *         description: Validation error or file upload error
 *       404:
 *         description: Trailer not found
 */
router.post('/trailer-photo', uploadSingle('photo'), asyncHandler(uploadController.uploadTrailerPhoto));

/**
 * @swagger
 * /upload/receipt-photo:
 *   post:
 *     summary: Upload a receipt photo
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - photo
 *               - capturedAt
 *             properties:
 *               photo:
 *                 type: string
 *                 format: binary
 *               capturedAt:
 *                 type: string
 *                 format: date-time
 *               latitude:
 *                 type: number
 *               longitude:
 *                 type: number
 *               accuracy:
 *                 type: number
 *     responses:
 *       201:
 *         description: Receipt photo uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     url:
 *                       type: string
 *                     filename:
 *                       type: string
 *                 message:
 *                   type: string
 *       400:
 *         description: Validation error or file upload error
 */
router.post('/receipt-photo', uploadSingle('photo'), asyncHandler(uploadController.uploadReceiptPhoto));

/**
 * @swagger
 * /upload/photos/{id}:
 *   delete:
 *     summary: Delete a photo
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Photo ID
 *     responses:
 *       200:
 *         description: Photo deleted successfully
 *       404:
 *         description: Photo not found
 */
router.delete('/photos/:id', asyncHandler(uploadController.deletePhoto));

export default router;
