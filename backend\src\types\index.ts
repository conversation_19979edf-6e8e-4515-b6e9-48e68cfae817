import { Request } from 'express';
import { User, Trailer, TireCondition, PhotoType, UserRole } from '@prisma/client';

// Extend Express Request to include user
export interface AuthenticatedRequest extends Request {
  user?: User;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Auth types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  name: string;
}

export interface AuthResponse {
  user: Omit<User, 'password'>;
  token: string;
}

// Trailer types
export interface CreateTrailerRequest {
  licensePlate: string;
  nickname: string;
  driverName: string;
  isRefrigerated: boolean;
  fuelLevelPercent?: number;
  damageDetails?: string;
  hookCount: number;
  europalletCount: number;
  loadBarCount: number;
  lastInspectionDate: string;
  tires: CreateTireRequest[];
}

export interface UpdateTrailerRequest extends Partial<CreateTrailerRequest> {
  id: string;
}

export interface CreateTireRequest {
  position: string;
  name: string;
  condition: TireCondition;
  pressure?: number;
  depth?: number;
}

// Photo types
export interface PhotoMetadata {
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  capturedAt: string;
  latitude?: number;
  longitude?: number;
  accuracy?: number;
}

export interface UploadPhotoRequest {
  type: PhotoType;
  trailerId?: string;
  capturedAt: string;
  latitude?: number;
  longitude?: number;
  accuracy?: number;
}

// Tire Inspection types
export interface CreateTireInspectionRequest {
  trailerId: string;
  inspectionDate: string;
  odometerKm?: number;
  purpose: string;
  mechanicName: string;
  recommendedPressure?: number;
}

// Tractor Refueling types
export interface CreateTractorRefuelingRequest {
  tractorLicensePlate: string;
  refuelingDate: string;
  dieselLiters?: number;
  adblueLiters?: number;
  odometerKm?: number;
  receiptPhotoUrl?: string;
}

// Refrigeration Refueling types
export interface CreateRefrigerationRefuelingRequest {
  trailerId: string;
  refuelingDate: string;
  dieselLiters?: number;
  fridgeMth?: number;
  receiptPhotoUrl?: string;
}

// Query parameters
export interface PaginationQuery {
  page?: string;
  limit?: string;
}

export interface TrailerQuery extends PaginationQuery {
  search?: string;
  isRefrigerated?: string;
  driverName?: string;
}

// Error types
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Validation error type
export interface ValidationError {
  field: string;
  message: string;
}

// File upload types
export interface MulterFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination: string;
  filename: string;
  path: string;
  buffer: Buffer;
}

// Export Prisma types
export {
  User,
  Trailer,
  TireCondition,
  PhotoType,
  UserRole
} from '@prisma/client';
